import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/views/profile_view/widgets/profile_shimmer.dart';
import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';
import 'package:room_eight/models/profile_model/profile_model.dart';
import '../../viewmodels/profile_bloc/profile_bloc.dart';

class ProfileScreen extends StatelessWidget {
  static Widget builder(BuildContext context) => const ProfileScreen();

  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).customColors.scaffoldColor,
      appBar: _buildAppbar(context),
      body: BlocBuilder<ProfileBloc, ProfileState>(
        builder: (context, state) {
          if (state.isGetUserProfileLoading) {
            return const ProfileScreenShimmer();
          }
          return _buildBody(context, state);
        },
      ),
    );
  }

  SingleChildScrollView _buildBody(BuildContext context, ProfileState state) {
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeaderInfo(context, state),
          buildSizedBoxH(16.h),
          _buildGenderAndAgeRow(context, state),
          buildSizedBoxH(16.h),
          _buildPersonalityContainer(context, state),
          buildSizedBoxH(16.h),
          _buildContactNumber(context, state),
          buildSizedBoxH(16.h),
          _buildLeasePeriodField(context, state),
          buildSizedBoxH(16.h),
          _buildPhotoGallery(context, state),
          buildSizedBoxH(110.h),
        ],
      ),
    );
  }

  RoomEightAppBar _buildAppbar(BuildContext context) {
    return RoomEightAppBar(
      backgroundColor: Theme.of(context).customColors.scaffoldColor,
      showBackButton: true,
      useGradientLeading: true,
      title: Lang.of(context).lbl_profile,
      centerTitle: true,
      ontap: () {
        context.read<NavBloc>().add(const NavTabChanged(0));
      },
      actions: [
        GestureDetector(
          onTap: () => NavigatorService.pushNamed(AppRoutes.editProfileScreen),
          child: Container(
            margin: const EdgeInsets.only(right: 16.0),
            height: 36.h,
            width: 36.w,
            child: CustomGradientContainer(
              height: 36.h,
              width: 36.w,
              topColor: Theme.of(context).customColors.fillColor!,
              bottomColor: Theme.of(context).customColors.fillColor!,
              fillColor: Theme.of(context).customColors.fillColor!,
              child: CustomImageView(
                imagePath: Assets.images.svgs.icons.icEdit.path,
                margin: const EdgeInsets.all(1.5),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeaderInfo(BuildContext context, ProfileState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          Center(
            child: Stack(
              alignment: Alignment.center,
              children: [
                CustomImageView(
                  height: 130.h,
                  width: 130.h,
                  imagePath: Assets.images.svgs.other.icProfileFrame.path,
                ),
                Align(
                  alignment: Alignment.center,
                  child: CustomImageView(
                    height: 100.h,
                    width: 100.h,
                    fit: BoxFit.cover,
                    radius: BorderRadius.circular(100.r),
                    imagePath: ApiEndPoint.getImageUrl + state.profileImagePath,
                  ),
                ),
              ],
            ),
          ),
          buildSizedBoxH(16.h),
          Text(
            state.nameController.text.isNotEmpty
                ? state.nameController.text
                : Lang.of(context).lbl_dash,
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
              color: Theme.of(context).customColors.blackColor,
              fontWeight: FontWeight.bold,
              fontSize: 20.sp,
            ),
          ),
          buildSizedBoxH(4.h),
          Text(
            Prefobj.preferences!
                    .get(Prefkeys.USER_MAIL_ID)
                    .toString()
                    .isNotEmpty
                ? Prefobj.preferences?.get(Prefkeys.USER_MAIL_ID)
                : Lang.of(context).lbl_dash,
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
              color: Theme.of(context).customColors.darkGreytextcolor,
              fontWeight: FontWeight.w500,
              fontSize: 14.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenderAndAgeRow(BuildContext context, ProfileState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).customColors.fillColor,
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).customColors.primaryColor!.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: CustomImageView(
                          height: 24.h,
                          imagePath: Assets.images.svgs.icons.icMale.path,
                          color: Theme.of(context).customColors.primaryColor,
                        ),
                      ),
                    ),
                    buildSizedboxW(16.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          Lang.of(context).lbl_gender,
                          style: Theme.of(context).textTheme.bodySmall!
                              .copyWith(
                                color: Theme.of(
                                  context,
                                ).customColors.darkGreytextcolor,
                                fontWeight: FontWeight.w500,
                                fontSize: 14.sp,
                              ),
                        ),
                        buildSizedBoxH(4.h),
                        Text(
                          state.selectedGender ?? Lang.of(context).lbl_dash,
                          style: Theme.of(context).textTheme.bodySmall!
                              .copyWith(
                                color: Theme.of(
                                  context,
                                ).customColors.blackColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 16.sp,
                              ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          buildSizedboxW(16.w),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).customColors.fillColor,
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).customColors.primaryColor!.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: CustomImageView(
                          height: 24.h,
                          imagePath: Assets.images.svgs.icons.icPerson.path,
                          color: Theme.of(context).customColors.primaryColor,
                        ),
                      ),
                    ),
                    buildSizedboxW(16.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          Lang.of(context).lbl_age,
                          style: Theme.of(context).textTheme.bodySmall!
                              .copyWith(
                                color: Theme.of(
                                  context,
                                ).customColors.darkGreytextcolor,
                                fontWeight: FontWeight.w500,
                                fontSize: 14.sp,
                              ),
                        ),
                        buildSizedBoxH(4.h),
                        Text(
                          state.ageController.text.isNotEmpty
                              ? state.ageController.text
                              : Lang.of(context).lbl_dash,
                          style: Theme.of(context).textTheme.bodySmall!
                              .copyWith(
                                color: Theme.of(
                                  context,
                                ).customColors.blackColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 16.sp,
                              ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalityContainer(BuildContext context, ProfileState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.fillColor,
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                Lang.of(context).lbl_personality,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                  color: Theme.of(context).customColors.blackColor,
                  fontWeight: FontWeight.w500,
                  fontSize: 16.sp,
                ),
              ),
              buildSizedBoxH(8.h),
              _buildPersonalityChips(context, state),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPersonalityChips(BuildContext context, ProfileState state) {
    // Get selected personality options from all categories
    final selectedOptions = <ProfileOptionModel>[];

    // Add selected habits & lifestyle
    for (int id in state.selectedHabitsAndLifestyle) {
      final option = state.habitsLifestyle.firstWhere(
        (option) => option.id == id,
        orElse: () => ProfileOptionModel(id: 0, name: '', icon: ''),
      );
      if (option.name?.isNotEmpty == true) {
        selectedOptions.add(option);
      }
    }

    // Add selected living style
    for (int id in state.selectedCleanlinessLivingStyle) {
      final option = state.livingStyle.firstWhere(
        (option) => option.id == id,
        orElse: () => ProfileOptionModel(id: 0, name: '', icon: ''),
      );
      if (option.name?.isNotEmpty == true) {
        selectedOptions.add(option);
      }
    }

    // Add selected interests & hobbies
    for (int id in state.selectedInterestsHobbies) {
      final option = state.interestsHobbies.firstWhere(
        (option) => option.id == id,
        orElse: () => ProfileOptionModel(id: 0, name: '', icon: ''),
      );
      if (option.name?.isNotEmpty == true) {
        selectedOptions.add(option);
      }
    }

    if (selectedOptions.isEmpty) {
      return Text(
        Lang.of(context).lbl_dash,
        style: Theme.of(context).textTheme.bodySmall!.copyWith(
          color: Theme.of(context).customColors.darkGreytextcolor,
          fontWeight: FontWeight.w500,
        ),
      );
    }

    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      children: selectedOptions
          .map((option) => _buildPersonalityChip(context, option))
          .toList(),
    );
  }

  Widget _buildPersonalityChip(
    BuildContext context,
    ProfileOptionModel option,
  ) {
    return Chip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomImageView(
            imagePath: ApiEndPoint.getImageUrl + (option.icon ?? ''),
            height: 20.h,
            width: 20.w,
            radius: BorderRadius.circular(12.r),
          ),
          buildSizedboxW(5.w),
          Text(
            option.name ?? '',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).customColors.fillColor,
              fontSize: 14.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
      backgroundColor: Theme.of(context).customColors.primaryColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30.r),
        side: BorderSide(color: Colors.transparent, width: 0),
      ),
    );
  }

  Widget _buildContactNumber(BuildContext context, ProfileState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            Lang.of(context).lbl_contact_number,
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
              color: Theme.of(context).customColors.blackColor,
              fontWeight: FontWeight.w500,
              fontSize: 16.sp,
            ),
          ),
          buildSizedBoxH(8.h),
          CustomTextInputField(
            context: context,
            controller: state.contactNumberController,
            hintLabel: Lang.of(context).lbl_emptyPhoneNumber,
            type: InputType.phoneNumber,
            readOnly: true,
            enabled: false,
            prefixIcon: CustomImageView(
              imagePath: Assets.images.svgs.icons.icPhone.path,
              margin: EdgeInsets.all(14.0),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLeasePeriodField(BuildContext context, ProfileState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            Lang.of(context).lbl_lease_period,
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
              color: Theme.of(context).customColors.blackColor,
              fontWeight: FontWeight.w500,
              fontSize: 16.sp,
            ),
          ),
          buildSizedBoxH(8.h),
          CustomTextInputField(
            context: context,
            controller: state.leasePeriodController,
            hintLabel: Lang.of(context).lbl_select_lease_period,
            type: InputType.text,
            readOnly: true,
            enabled: false,
            prefixIcon: CustomImageView(
              imagePath: Assets.images.svgs.icons.icHomeTag.path,
              margin: EdgeInsets.all(14.0),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoGallery(BuildContext context, ProfileState state) {
    if (state.photoPaths.isEmpty) {
      return SizedBox.shrink();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            Lang.of(context).lbl_photo_upload,
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
              color: Theme.of(context).customColors.blackColor,
              fontWeight: FontWeight.w500,
              fontSize: 16.sp,
            ),
          ),
        ),
        buildSizedBoxH(8.h),
        SizedBox(
          height: 90.h,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: state.photoPaths.length,
            separatorBuilder: (_, __) => SizedBox(width: 8.w),
            padding: const EdgeInsets.only(right: 16.0),
            itemBuilder: (context, index) {
              final photoPath = state.photoPaths[index];
              return Padding(
                padding: EdgeInsets.only(left: index == 0 ? 16.0 : 0.0),
                child: Container(
                  height: 90.h,
                  width: 80.w,
                  decoration: BoxDecoration(
                    color: Theme.of(context).customColors.fillColor,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).customColors.primaryColor!,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CustomImageView(
                      imagePath: ApiEndPoint.getImageUrl + photoPath,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
